              param(
                [Parameter(Mandatory=$true)] $reference,
                [Parameter(Mandatory=$true)] $requested_by,
                [Parameter(Mandatory=$true)] $requested_on,
                [Parameter(Mandatory=$true)] $catagory,
                [Parameter(Mandatory=$true)] $record_type,
                [Parameter(Mandatory=$true)] $act_date,
                [Parameter(Mandatory=$true)] $ptr,
                [Parameter(Mandatory=$true)] $hostname,
                [Parameter(Mandatory=$true)] $ip_address,
                [Parameter(Mandatory=$true)] $host_alias,
                [Parameter(Mandatory=$true)] $new_hostname,
                [Parameter(Mandatory=$true)] $new_ip_address,
                [Parameter(Mandatory=$true)] $new_host_alias,
                [Parameter(Mandatory=$true)] $status,
                [Parameter(Mandatory=$true)] $completed_datetime,
                [Parameter(Mandatory=$true)] $jobTask,
                [Parameter(Mandatory=$true)] $success
              )

              $DB = "MariaDB"
              $DBtableTemplate = [ordered]@{Server="";Database="";Port="";User="";Password="";Credential="";Driver="";DLL="";Option=""}
              $dbTable = @{}
              $dbObj = New-Object psobject -Property $DBtableTemplate
              $dbobj.Database = "dcsPRDautomation"
              $dbobj.Server = "mdbpktipamcon.sanlam.co.za"
              $dbobj.Port = 9433

              # Import credential and reconstruct as proper PSCredential object
              $importedCred = Import-Clixml -Path "D:\AutoDeploy\RequiredFiles\Credentials\prd_db_creds.xml"

              # Handle both old format (UserName) and new format (Username)
              $userName = if ($importedCred.UserName) { $importedCred.UserName } else { $importedCred.Username }

              # Check if Password is already a SecureString or needs conversion
              if ($importedCred.Password -is [System.Security.SecureString]) {
                  $securePassword = $importedCred.Password
              } else {
                  # Password is encrypted string - convert to SecureString
                  $securePassword = $importedCred.Password | ConvertTo-SecureString
              }
              $dbobj.Credential = New-Object System.Management.Automation.PSCredential($userName, $securePassword)

              $dbObj.Driver = "{MariaDB ODBC 3.1 Driver}"
              $dbobj.Option = 67108867
              $dbTable.Add("MariaDB",$dbobj)
              $dbConnection = $NULL

              function connectdb {
                  if ($null -eq $script:DBconnection) {
                      $script:DBconnection = New-Object System.Data.Odbc.OdbcConnection
                      # Option 1: Separate PORT parameter (recommended for MariaDB ODBC 3.1)
                      $script:DBconnection.ConnectionString = "DRIVER={0};Server = {1};Port = {2};Database = {3};UID = {4};PWD = ***;Option = {6}" -f $dbTable[$db].Driver,$dbTable[$db].Server,$dbTable[$db].Port,$dbTable[$db].Database,$dbTable[$db].Credential.UserName,$dbTable[$db].Credential.GetNetworkCredential().Password,$dbTable[$db].Option

                      # Option 2: If above doesn't work, uncomment this line and comment out the line above
                      # $script:DBconnection.ConnectionString = "DRIVER={0};Server = {1}:{2};Database = {3};UID = {4};PWD = ***;Option = {6}" -f $dbTable[$db].Driver,$dbTable[$db].Server,$dbTable[$db].Port,$dbTable[$db].Database,$dbTable[$db].Credential.UserName,$dbTable[$db].Credential.GetNetworkCredential().Password,$dbTable[$db].Option
                  }
              }
              function opendb {
                  $success = $false
                  if ($null -eq $DBconnection) {
                    connectdb
                  }
                  if ($DBconnection.State -eq "Closed") {
                    try {
                      $DBconnection.open()
                      $success = $true
                    }
                    catch {
                      Write-Error "Unable to sign in to database: $($_.Exception.Message)"
                    }
                  }
                  return $success
              }

              function Escape-SqlString ([string]$value) {
                # Escape single quotes for SQL
                return $value.Replace("'", "''")
              }

              function dbQuery ([string]$query) {
                try {
                  $dbCommand = $script:DBconnection.CreateCommand()
                  $dbCommand.CommandText = $query
                  [void]$dbCommand.ExecuteNonQuery()
                  $dbCommand.Dispose()

                  $xlrSuccess = $true
                  $xlrStatus = "NORMAL"
                  $xlrMessage = "Query into database successful"
                }
                catch {
                  $xlrSuccess = $false
                  $xlrStatus = "REMEDIATION"
                  $xlrMessage = "Failed to query into the database: $($_.Exception.Message)"
                }

                $xlrResponse = "" | Select-Object success, status, message, data
                $xlrResponse.success = $xlrSuccess
                $xlrResponse.status = $xlrStatus
                $xlrResponse.message = $xlrMessage
                $xlrResponse.data = $query

                return $xlrResponse
              }

              if($null -eq $requested_on){
                  $requested_on_final = ""
              }else{
                  $requested_on_final = (Get-Date -Format 'yyyy-MM-ddTHH:mm:00')
              }

              if($null -eq $completed_datetime){
                  $completed_datetime_final = ""
              }else{
                  $completed_datetime_final = (Get-Date -Format 'yyyy-MM-ddTHH:mm:00')
              }

              if($ptr -match "false"){
                $ptr = 0
              }else{
                  $ptr = 1
              }

              $info = [pscustomobject] @{
                "reference"= $reference
                "requested_by"= $requested_by
                "requested_on"= $requested_on_final
                "catagory"= $catagory
                "record_type"= $record_type
                "act_date"= $act_date
                "ptr"= $ptr
                "hostname"= $hostname
                "ip_address"= $ip_address
                "host_alias"= $host_alias
                "new_hostname"= $new_hostname
                "new_ip_address"= $new_ip_address
                "new_host_alias"= $new_host_alias
                "dns_status"= $status
                "completed_datetime"= $completed_datetime_final
                "success"= $success
              }

              if ((opendb) -eq $false) {
                $xlrSuccess = $false
                $xlrStatus = "REMEDIATION"
                $xlrMessage = "Failed to Connect to database"
              }else{

                if($jobTask -eq "ADD_JOB"){
                  # Escape all string values to prevent SQL injection and handle special characters
                  $esc_reference = Escape-SqlString $info.reference
                  $esc_requested_by = Escape-SqlString $info.requested_by
                  $esc_requested_on = if ($info.requested_on) { Escape-SqlString $info.requested_on } else { "" }
                  $esc_catagory = Escape-SqlString $info.catagory
                  $esc_record_type = Escape-SqlString $info.record_type
                  $esc_act_date = Escape-SqlString $info.act_date
                  $esc_hostname = Escape-SqlString $info.hostname
                  $esc_ip_address = Escape-SqlString $info.ip_address
                  $esc_host_alias = Escape-SqlString $info.host_alias
                  $esc_new_hostname = Escape-SqlString $info.new_hostname
                  $esc_new_ip_address = Escape-SqlString $info.new_ip_address
                  $esc_new_host_alias = Escape-SqlString $info.new_host_alias
                  $esc_dns_status = Escape-SqlString $info.dns_status

                  # Build query with proper NULL handling for empty dates
                  $requested_on_value = if ($esc_requested_on) { "'$esc_requested_on'" } else { "NULL" }

                  $query = "INSERT INTO dnsrequests (reference, requested_by, requested_on, catagory, record_type, act_date, ptr, hostname, ip_address, host_alias, new_hostname, new_ip_address, new_host_alias, dns_status) VALUES ('$esc_reference', '$esc_requested_by', $requested_on_value, '$esc_catagory', '$esc_record_type', '$esc_act_date', $($info.ptr), '$esc_hostname', '$esc_ip_address', '$esc_host_alias', '$esc_new_hostname', '$esc_new_ip_address', '$esc_new_host_alias', '$esc_dns_status')"

                  $queryStuff = dbQuery $query
                }elseif($jobTask -eq "UPDATE_JOB"){
                  $esc_dns_status = Escape-SqlString $info.dns_status
                  $esc_success = Escape-SqlString $info.success
                  $esc_completed_datetime = if ($info.completed_datetime) { Escape-SqlString $info.completed_datetime } else { "" }
                  $esc_reference = Escape-SqlString $info.reference

                  $completed_datetime_value = if ($esc_completed_datetime) { "'$esc_completed_datetime'" } else { "NULL" }

                  $query = "UPDATE dnsrequests SET dns_status = '$esc_dns_status', success = '$esc_success', completed_datetime = $completed_datetime_value WHERE reference = '$esc_reference'"

                  $queryStuff = dbQuery $query
                }

                $xlrSuccess = $queryStuff.success
                $xlrStatus = $queryStuff.status
                $xlrMessage = $queryStuff.message
                $xlrData = $queryStuff.data

              }
              $xlrResponse = "" | Select-Object success, status, message, data
              $xlrResponse.success = $xlrSuccess
              $xlrResponse.status = $xlrStatus
              $xlrResponse.message = $xlrMessage
              $xlrResponse.data = $xlrData
              return $($xlrResponse | ConvertTo-Json)
