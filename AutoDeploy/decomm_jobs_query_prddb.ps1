param(
  [Parameter(Mandatory=$true)] $reference,
  [Parameter(Mandatory=$true)] $external_ref,
  [Parameter(Mandatory=$true)] $changeref,
  [Parameter(Mandatory=$true)] $hostname,
  [Parameter(Mandatory=$true)] $os_version,
  [Parameter(Mandatory=$true)] $competency,
  [Parameter(Mandatory=$true)] $hosting_platform,
  [Parameter(Mandatory=$true)] $decomm_stage,
  [Parameter(Mandatory=$true)] $is_shutdown,
  [Parameter(Mandatory=$true)] $shutdown_date,
  [Parameter(Mandatory=$true)] $delete_date,
  [Parameter(Mandatory=$false)] $start_date,
  [Parameter(Mandatory=$false)] $end_date,
  [Parameter(Mandatory=$true)] $requested_by,
  [Parameter(Mandatory=$true)] $external_requester,
  [Parameter(Mandatory=$false)] $decomm_comments,
  [Parameter(Mandatory=$true)] $jobTask,
  [Parameter(Mandatory=$true)] $method
)

$DB = "MariaDB"
$DBtableTemplate = [ordered]@{Server="";Database="";Port="";User="";Password="";Credential="";Driver="";DLL="";Option=""}
$dbTable = @{}
$dbObj = New-Object psobject -Property $DBtableTemplate
$dbobj.Database = "dcsPRDautomation"
$dbobj.Server = "mdbpktipamcon.sanlam.co.za"
$dbobj.Port = 9433

# Import credential and reconstruct as proper PSCredential object
$importedCred = Import-Clixml -Path "D:\AutoDeploy\RequiredFiles\Credentials\prd_db_creds.xml"

# Handle both old format (UserName) and new format (Username)
$userName = if ($importedCred.UserName) { $importedCred.UserName } else { $importedCred.Username }

# Check if Password is already a SecureString or needs conversion
if ($importedCred.Password -is [System.Security.SecureString]) {
    $securePassword = $importedCred.Password
} else {
    # Password is encrypted string - convert to SecureString
    $securePassword = $importedCred.Password | ConvertTo-SecureString
}
$dbobj.Credential = New-Object System.Management.Automation.PSCredential($userName, $securePassword)

$dbObj.Driver = "{MariaDB ODBC 3.1 Driver}"
$dbobj.Option = 67108867
$dbTable.Add("MariaDB",$dbobj)
$dbConnection = $NULL

function connectdb {
    if ($null -eq $script:DBconnection) {
        $script:DBconnection = New-Object System.Data.Odbc.OdbcConnection
        # Option 1: Separate PORT parameter (recommended for MariaDB ODBC 3.1)
        $script:DBconnection.ConnectionString = "DRIVER={0};Server = {1};Port = {2};Database = {3};UID = {4};PWD = ***;Option = {6}" -f $dbTable[$db].Driver,$dbTable[$db].Server,$dbTable[$db].Port,$dbTable[$db].Database,$dbTable[$db].Credential.UserName,$dbTable[$db].Credential.GetNetworkCredential().Password,$dbTable[$db].Option

        # Option 2: If above doesn't work, uncomment this line and comment out the line above
        # $script:DBconnection.ConnectionString = "DRIVER={0};Server = {1}:{2};Database = {3};UID = {4};PWD = ***;Option = {6}" -f $dbTable[$db].Driver,$dbTable[$db].Server,$dbTable[$db].Port,$dbTable[$db].Database,$dbTable[$db].Credential.UserName,$dbTable[$db].Credential.GetNetworkCredential().Password,$dbTable[$db].Option
    }
}
function opendb {
    $success = $false
    if ($null -eq $DBconnection) {
      connectdb
    }
    if ($DBconnection.State -eq "Closed") {
      try {
        $DBconnection.open()
        $success = $true
      }
      catch {
        Write-Error "Unable to sign in to database: $($_.Exception.Message)"
      }
    }
    return $success
}

function Escape-SqlString ([string]$value) {
  # Escape single quotes for SQL
  return $value.Replace("'", "''")
}

function dbQuery ([string]$query) {
  try {
    $dbCommand = $script:DBconnection.CreateCommand()
    $dbCommand.CommandText = $query
    [void]$dbCommand.ExecuteNonQuery()
    $dbCommand.Dispose()

    $xlrSuccess = $true
    $xlrStatus = "NORMAL"
    $xlrMessage = "Query into database successful"
  }
  catch {
    $xlrSuccess = $false
    $xlrStatus = "REMEDIATION"
    $xlrMessage = "Failed to query into the database: $($_.Exception.Message)"
  }

  $xlrResponse = "" | Select-Object success, status, message, data
  $xlrResponse.success = $xlrSuccess
  $xlrResponse.status = $xlrStatus
  $xlrResponse.message = $xlrMessage
  $xlrResponse.data = $query

  return $xlrResponse
}

if($null -eq $end_date){
  $end_date_final = ""
}else{
  $end_date_final = (Get-Date -Format 'yyyy-MM-ddTHH:mm:00')
}

if($null -eq $start_date){
  $start_date_final = ""
}else{
  $start_date_final = (Get-Date -Format 'yyyy-MM-ddTHH:mm:00')
}

if($is_shutdown -match "false"){
  $is_shutdown = 0
}else{
  $is_shutdown = 1
}

if($method -match "false"){
  $is_manual = "AUTO"
}else{
  $is_manual = "MANUAL"
}

$info = [pscustomobject] @{
  "reference"= $reference
  "external_ref"= $external_ref
  "changeref"= $changeref
  "hostname"= $hostname
  "os_version"= $os_version
  "competency"= $competency
  "hosting_platform"= $hosting_platform
  "decomm_stage"= $decomm_stage
  "is_shutdown"= $is_shutdown
  "shutdown_date"= $shutdown_date
  "delete_date"= $delete_date
  "start_date"= $start_date_final
  "end_date"= $end_date_final
  "managed_by"= $requested_by
  "requested_by"= $external_requester
  "decomm_method" = $is_manual
  "decomm_comments"= $decomm_comments
}

if ((opendb) -eq $false) {
  $xlrSuccess = $false
  $xlrStatus = "REMEDIATION"
  $xlrMessage = "Failed to Connect to database"
}else{

  if($jobTask -eq "ADD_JOB"){
    # Escape all string values to prevent SQL injection and handle special characters
    $esc_reference = Escape-SqlString $info.reference
    $esc_external_ref = Escape-SqlString $info.external_ref
    $esc_changeref = Escape-SqlString $info.changeref
    $esc_hostname = Escape-SqlString $info.hostname
    $esc_os_version = Escape-SqlString $info.os_version
    $esc_competency = Escape-SqlString $info.competency
    $esc_hosting_platform = Escape-SqlString $info.hosting_platform
    $esc_decomm_stage = Escape-SqlString $info.decomm_stage
    $esc_shutdown_date = Escape-SqlString $info.shutdown_date
    $esc_delete_date = Escape-SqlString $info.delete_date
    $esc_start_date = if ($info.start_date) { Escape-SqlString $info.start_date } else { "" }
    $esc_end_date = if ($info.end_date) { Escape-SqlString $info.end_date } else { "" }
    $esc_managed_by = Escape-SqlString $info.managed_by
    $esc_requested_by = Escape-SqlString $info.requested_by
    $esc_decomm_method = Escape-SqlString $info.decomm_method
    $esc_decomm_comments = Escape-SqlString $info.decomm_comments

    # Build query with proper NULL handling for empty dates
    $start_date_value = if ($esc_start_date) { "'$esc_start_date'" } else { "NULL" }
    $end_date_value = if ($esc_end_date) { "'$esc_end_date'" } else { "NULL" }

    $query = "INSERT INTO decommjobs (reference, external_ref, changeref, hostname, os_version, competency, hosting_platform, decomm_stage, is_shutdown, shutdown_date, delete_date, start_date, end_date, managed_by, requested_by, decomm_method, decomm_comments) VALUES ('$esc_reference', '$esc_external_ref', '$esc_changeref', '$esc_hostname', '$esc_os_version', '$esc_competency', '$esc_hosting_platform', '$esc_decomm_stage', $($info.is_shutdown), '$esc_shutdown_date', '$esc_delete_date', $start_date_value, $end_date_value, '$esc_managed_by', '$esc_requested_by', '$esc_decomm_method', '$esc_decomm_comments')"

    $queryStuff = dbQuery $query
  }elseif($jobTask -eq "UPDATE_JOB"){
    $esc_decomm_stage = Escape-SqlString $info.decomm_stage
    $esc_end_date = if ($info.end_date) { Escape-SqlString $info.end_date } else { "" }
    $esc_decomm_comments = Escape-SqlString $info.decomm_comments
    $esc_reference = Escape-SqlString $info.reference

    $end_date_value = if ($esc_end_date) { "'$esc_end_date'" } else { "NULL" }

    $query = "UPDATE decommjobs SET decomm_stage = '$esc_decomm_stage', is_shutdown = $($info.is_shutdown), end_date = $end_date_value, decomm_comments = '$esc_decomm_comments' WHERE reference = '$esc_reference'"

    $queryStuff = dbQuery $query
  }

  $xlrSuccess = $queryStuff.success
  $xlrStatus = $queryStuff.status
  $xlrMessage = $queryStuff.message
  $xlrData = $queryStuff.query

}
$xlrResponse = "" | Select-Object success, status, message, data
$xlrResponse.success = $xlrSuccess
$xlrResponse.status = $xlrStatus
$xlrResponse.message = $xlrMessage
$xlrResponse.data = $xlrData
return $($xlrResponse | ConvertTo-Json)
