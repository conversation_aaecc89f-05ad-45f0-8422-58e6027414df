param(
  [Parameter(Mandatory=$true)] $reference,
  [Parameter(Mandatory=$true)] $external_ref,
  [Parameter(Mandatory=$true)] $changeref,
  [Parameter(Mandatory=$true)] $hostname,
  [Parameter(Mandatory=$true)] $os_version,
  [Parameter(Mandatory=$true)] $competency,
  [Parameter(Mandatory=$true)] $hosting_platform,
  [Parameter(Mandatory=$true)] $decomm_stage,
  [Parameter(Mandatory=$true)] $is_shutdown,
  [Parameter(Mandatory=$true)] $shutdown_date,
  [Parameter(Mandatory=$true)] $delete_date,
  [Parameter(Mandatory=$false)] $start_date,
  [Parameter(Mandatory=$false)] $end_date,
  [Parameter(Mandatory=$true)] $requested_by,
  [Parameter(Mandatory=$true)] $external_requester,
  [Parameter(Mandatory=$false)] $decomm_comments,
  [Parameter(Mandatory=$true)] $jobTask,
  [Parameter(Mandatory=$true)] $method
)

$DB = "MariaDB"
$DBtableTemplate = [ordered]@{Server="";Database="";Port="";User="";Password="";Credential="";Driver="";DLL="";Option=""}
$dbTable = @{}
$dbObj = New-Object psobject -Property $DBtableTemplate
$dbobj.Database = "dcsDEVautomation"
$dbobj.Server = "**********"
$dbobj.Credential = Import-Clixml -Path "D:\AutoDeploy\RequiredFiles\Credentials\dev_db_creds.xml"
$dbObj.Driver = "{MariaDB ODBC 3.1 Driver}"
$dbobj.Option = 67108867
$dbTable.Add("MariaDB",$dbobj)
$dbConnection = $NULL

function connectdb {
    if ($null -eq $script:DBconnection) {
        $script:DBconnection = New-Object System.Data.Odbc.OdbcConnection
        $script:DBconnection.ConnectionString = "DRIVER={0};Server = {1};Database = {2};UID = {3};PWD = ***;Option = {5}" -f $dbTable[$db].Driver,$dbTable[$db].Server,$dbTable[$db].Database,$dbTable[$db].Credential.UserName,$dbTable[$db].Credential.GetNetworkCredential().Password,$dbTable[$db].Option
    }
}
function opendb {
    $success = $false
    if ($null -eq $DBconnection) {
      connectdb
    }
    if ($DBconnection.State -eq "Closed") {
      try {
        $DBconnection.open()
        $success = $true
      }
      catch {
        "Unable to sign in to database"
      }
    }
    return $success
}

function dbQuery ([string]$query) {
  try {
    $dbCommand = $script:DBconnection.CreateCommand()
    $dbCommand.CommandText = $query
    [void]$dbCommand.ExecuteNonQuery()
    $dbCommand.Dispose()

    $xlrSuccess = $true
    $xlrStatus = "NORMAL"
    $xlrMessage = "Query into database successful"    
  }
  catch {  
    $xlrSuccess = $false
    $xlrStatus = "REMEDIATION"
    $xlrMessage = "Failed to query into the database"
  }

  $xlrResponse = "" | Select-Object success, status, message, data
  $xlrResponse.success = $xlrSuccess
  $xlrResponse.status = $xlrStatus
  $xlrResponse.message = $xlrMessage
  $xlrResponse.data = $query

  return $xlrResponse
}

if($null -eq $end_date){
  $end_date_final = ""
}else{
  $end_date_final = (Get-Date -Format 'yyyy-MM-ddTHH:mm:00')
}

if($null -eq $start_date){
  $start_date_final = ""
}else{
  $start_date_final = (Get-Date -Format 'yyyy-MM-ddTHH:mm:00')
}

if($is_shutdown -match "false"){
  $is_shutdown = 0
}else{
  $is_shutdown = 1
}

if($method -match "false"){
  $is_manual = "AUTO"
}else{
  $is_manual = "MANUAL"
}

$info = [pscustomobject] @{
  "reference"= $reference
  "external_ref"= $external_ref
  "changeref"= $changeref
  "hostname"= $hostname
  "os_version"= $os_version
  "competency"= $competency
  "hosting_platform"= $hosting_platform
  "decomm_stage"= $decomm_stage
  "is_shutdown"= $is_shutdown
  "shutdown_date"= $shutdown_date
  "delete_date"= $delete_date
  "start_date"= $start_date_final
  "end_date"= $end_date_final
  "managed_by"= $requested_by
  "requested_by"= $external_requester
  "decomm_method" = $is_manual
  "decomm_comments"= $decomm_comments
}

if ((opendb) -eq $false) {
  $xlrSuccess = $false
  $xlrStatus = "REMEDIATION"
  $xlrMessage = "Failed to Connect to database"
}else{

  if($jobTask -eq "ADD_JOB"){
    $queryStuff = dbQuery ('INSERT INTO decommjobs (reference, external_ref, changeref, hostname, os_version, competency, hosting_platform, decomm_stage, is_shutdown, shutdown_date, delete_date, start_date, end_date, managed_by, requested_by, decomm_method, decomm_comments) VALUES ("{0}", "{1}", "{2}", "{3}", "***", "{5}", "{6}", "{7}", "{8}", "{9}", "{10}", "{11}", "{12}", "{13}", "{14}", "{15}", "{16}")' -f $info.reference, $info.external_ref, $info.changeref, $info.hostname, $info.os_version, $info.competency, $info.hosting_platform, $info.decomm_stage, $info.is_shutdown, $info.shutdown_date, $info.delete_date, $info.start_date, $info.end_date, $info.managed_by, $info.requested_by, $info.decomm_method, $info.decomm_comments)
  }elseif($jobTask -eq "UPDATE_JOB"){
    $queryStuff = dbQuery ('UPDATE decommjobs SET decomm_stage = "{0}", is_shutdown = "{1}", end_date = "{2}", decomm_comments = "{3}" WHERE reference = "***"' -f $info.decomm_stage, $info.is_shutdown, $info.end_date_final, $info.decomm_comments, $info.reference)
  }

  $xlrSuccess = $queryStuff.success
  $xlrStatus = $queryStuff.status
  $xlrMessage = $queryStuff.message
  $xlrData = $queryStuff.query

}
$xlrResponse = "" | Select-Object success, status, message, data
$xlrResponse.success = $xlrSuccess
$xlrResponse.status = $xlrStatus
$xlrResponse.message = $xlrMessage
$xlrResponse.data = $xlrData
return $($xlrResponse | ConvertTo-Json)
